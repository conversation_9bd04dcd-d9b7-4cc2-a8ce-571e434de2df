import React, { useState, useRef } from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { ScrollView } from "@/components/ui/scroll-view";
import { Calendar } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import CalendarWidget from "@/components/shared/calendar-widget";
import { Modal } from "react-native";
import { Box } from "@/components/ui/box";

interface HorizontalDatePickerProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

const HorizontalDatePicker: React.FC<HorizontalDatePickerProps> = ({
  selectedDate,
  onDateSelect,
}) => {
  const [showCalendar, setShowCalendar] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const dayNames = ["SU", "MO", "TU", "WE", "TH", "FR", "SA"];

  // Generate dates for the next 30 days
  const generateDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }

    return dates;
  };

  const dates = generateDates();

  const formatDate = (date: Date) => {
    const dayName = dayNames[date.getDay()];
    const dayNumber = date.getDate();
    return { dayName, dayNumber };
  };

  const isSelected = (date: Date) => {
    return (
      date.getDate() === selectedDate.getDate() &&
      date.getMonth() === selectedDate.getMonth() &&
      date.getFullYear() === selectedDate.getFullYear()
    );
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  const handleDatePress = (date: Date) => {
    onDateSelect(date);
  };

  const handleCalendarSelect = (date: Date) => {
    onDateSelect(date);
    setShowCalendar(false);
  };

  return (
    <>
      <HStack className="items-center px-4 py-2" space="md">
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          className="flex-1"
          contentContainerStyle={{ paddingRight: 16 }}
        >
          <HStack space="md">
            {dates.map((date, index) => {
              const { dayName, dayNumber } = formatDate(date);
              const selected = isSelected(date);
              const today = isToday(date);

              return (
                <Pressable
                  key={index}
                  onPress={() => handleDatePress(date)}
                  className="items-center"
                >
                  <VStack
                    className={`items-center px-3 py-2 rounded-2xl ${
                      selected ? "bg-primary-500" : "bg-transparent"
                    }`}
                    space="xs"
                  >
                    <Text
                      className={`text-xs font-dm-sans-medium ${
                        selected
                          ? "text-white"
                          : today
                          ? "text-typography-900"
                          : "text-typography-600"
                      }`}
                    >
                      {dayName}
                    </Text>
                    <Text
                      className={`text-lg font-dm-sans-bold ${
                        selected
                          ? "text-white"
                          : today
                          ? "text-typography-900"
                          : "text-typography-900"
                      }`}
                    >
                      {dayNumber}
                    </Text>
                    {selected && (
                      <Box className="w-1 h-1 bg-white rounded-full" />
                    )}
                  </VStack>
                </Pressable>
              );
            })}
          </HStack>
        </ScrollView>

        {/* Calendar Icon */}
        <Pressable
          onPress={() => setShowCalendar(true)}
          className="p-3 bg-white rounded-xl border border-background-200"
        >
          <Icon as={Calendar} size="lg" className="text-typography-600" />
        </Pressable>
      </HStack>

      {/* Full Calendar Modal */}
      <Modal
        visible={showCalendar}
        transparent
        animationType="fade"
        onRequestClose={() => setShowCalendar(false)}
      >
        <Pressable
          className="flex-1 bg-black/50 justify-center items-center"
          onPress={() => setShowCalendar(false)}
        >
          <Pressable
            className="bg-white rounded-2xl m-4 max-w-sm w-full"
            onPress={(e) => e.stopPropagation()}
          >
            <CalendarWidget
              selectedDate={selectedDate}
              onDateSelect={handleCalendarSelect}
            />
          </Pressable>
        </Pressable>
      </Modal>
    </>
  );
};

export default HorizontalDatePicker;
