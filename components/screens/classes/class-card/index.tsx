import React from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Pressable } from "@/components/ui/pressable";
import { Box } from "@/components/ui/box";
import { Heart, Clock } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { ClassItem } from "@/data/screens/classes";
import { router } from "expo-router";

interface ClassCardProps extends ClassItem {}

const ClassCard: React.FC<ClassCardProps> = ({
  id,
  title,
  type,
  instructor,
  time,
  duration,
  status,
  isFavorite,
  avatar,
}) => {
  const getStatusButton = () => {
    switch (status) {
      case "available":
        return (
          <Button
            size="sm"
            className="bg-primary-500 border-0 px-4 py-2 rounded-lg"
            onPress={(e) => {
              e.stopPropagation();
              // Handle reservation logic here
            }}
          >
            <ButtonText className="text-white font-dm-sans-medium text-sm">
              Reserve
            </ButtonText>
          </Button>
        );
      case "cancelled":
        return (
          <Text className="text-sm font-dm-sans-medium text-error-500 bg-error-50 px-3 py-1 rounded-lg">
            Cancel reservation
          </Text>
        );
      case "waitlist":
        return (
          <Button
            size="sm"
            variant="outline"
            className="border-success-300 bg-success-50 px-4 py-2 rounded-lg"
            onPress={(e) => {
              e.stopPropagation();
              // Handle waitlist logic here
            }}
          >
            <ButtonText className="text-success-600 font-dm-sans-medium text-sm">
              Join waitlist
            </ButtonText>
          </Button>
        );
      case "reserved":
        return (
          <Button
            size="sm"
            className="bg-primary-500 border-0 px-4 py-2 rounded-lg"
            onPress={(e) => {
              e.stopPropagation();
              // Handle reservation view logic here
            }}
          >
            <ButtonText className="text-white font-dm-sans-medium text-sm">
              Reserve
            </ButtonText>
          </Button>
        );
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case "cancelled":
        return "text-error-500";
      case "waitlist":
        return "text-success-600";
      default:
        return "text-typography-600";
    }
  };

  const handleCardPress = () => {
    router.push(`/class-details/${id}`);
  };

  return (
    <Pressable onPress={handleCardPress}>
      <VStack
        space="sm"
        className="bg-white rounded-2xl p-4 border border-background-200"
      >
        {/* Header with avatar, title, and favorite */}
        <HStack className="justify-between items-start">
          <HStack space="sm" className="flex-1">
            {/* Avatar */}
            <Box className="w-12 h-12 bg-background-100 rounded-full items-center justify-center">
              <Text className="text-sm font-dm-sans-bold text-typography-900">
                {avatar}
              </Text>
            </Box>

            {/* Title and type */}
            <VStack className="flex-1" space="xs">
              <HStack className="items-center" space="xs">
                <Text className="text-base font-dm-sans-bold text-typography-900">
                  {title}
                </Text>
                <Box
                  className={`px-2 py-1 rounded ${
                    type === "Paid" ? "bg-primary-100" : "bg-success-100"
                  }`}
                >
                  <Text
                    className={`text-xs font-dm-sans-medium ${
                      type === "Paid" ? "text-primary-600" : "text-success-600"
                    }`}
                  >
                    {type}
                  </Text>
                </Box>
              </HStack>

              {/* Time and duration */}
              <HStack className="items-center" space="xs">
                <Text
                  className={`text-sm font-dm-sans-medium ${getStatusColor()}`}
                >
                  {time}
                </Text>
                <Text className="text-typography-400">•</Text>
                <HStack className="items-center" space="xs">
                  <Icon as={Clock} size="xs" className="text-typography-400" />
                  <Text className="text-sm font-dm-sans-regular text-typography-400">
                    {duration}
                  </Text>
                </HStack>
              </HStack>
            </VStack>
          </HStack>

          {/* Favorite button */}
          <Pressable
            className="p-2"
            onPress={(e) => {
              e.stopPropagation();
              // Handle favorite toggle here
            }}
          >
            <Icon
              as={Heart}
              size="sm"
              className={
                isFavorite
                  ? "text-error-500 fill-error-500"
                  : "text-typography-400"
              }
            />
          </Pressable>
        </HStack>

        {/* Instructor and action button */}
        <HStack className="justify-between items-center mt-2">
          <Text className="text-sm font-dm-sans-regular text-typography-600">
            {instructor}
          </Text>
          {getStatusButton()}
        </HStack>

        {/* Status text for cancelled classes */}
        {status === "cancelled" && (
          <Text className="text-xs font-dm-sans-regular text-typography-400 mt-1">
            Cut due to low family
          </Text>
        )}
      </VStack>
    </Pressable>
  );
};

export default ClassCard;
