import React from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input";
import { Pressable } from "@/components/ui/pressable";
import {
  Search,
  SlidersHorizontal,
  MapPin,
  ChevronDown,
} from "lucide-react-native";
import { Icon } from "@/components/ui/icon";

const ClassesHeader: React.FC = () => {
  return (
    <VStack space="md" className="px-4 pt-4 pb-2">
      {/* Title */}
      <HStack className="justify-between items-center">
        <Text className="text-2xl font-dm-sans-bold text-typography-900">
          Classes & Appointments
        </Text>
        <Pressable className="p-2">
          <Icon
            as={SlidersHorizontal}
            size="lg"
            className="text-typography-600"
          />
        </Pressable>
      </HStack>

      {/* Search Bar */}
      <Input
        variant="outline"
        className="border-outline-200 bg-white rounded-xl"
        size="lg"
      >
        <InputSlot>
          <InputIcon as={Search} className="text-typography-400 ml-3" />
        </InputSlot>
        <InputField
          placeholder="Search"
          className="placeholder:text-typography-400"
        />
        <InputSlot>
          <Pressable className="p-2 bg-background-100 rounded-lg mr-2 border border-background-200">
            <Icon
              as={SlidersHorizontal}
              size="sm"
              className="text-typography-600"
            />
          </Pressable>
        </InputSlot>
      </Input>

      {/* Location Dropdown */}
      <HStack className="items-center" space="sm">
        <Pressable className="flex-row items-center space-x-2 px-3 py-2">
          <Icon as={MapPin} size="sm" className="text-typography-600" />
          <Text className="text-sm font-dm-sans-medium text-typography-600">
            Collins & catz family
          </Text>
          <Icon as={ChevronDown} size="sm" className="text-typography-600" />
        </Pressable>
      </HStack>
    </VStack>
  );
};

export default ClassesHeader;
