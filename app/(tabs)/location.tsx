import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { ScrollView } from "@/components/ui/scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import ClassesHeader from "@/components/screens/classes/classes-header";
import HorizontalDatePicker from "@/components/shared/horizontal-date-picker";
import ClassesTabs from "@/components/screens/classes/classes-tabs";
import ClassCard from "@/components/screens/classes/class-card";
import { classesData, ClassItem } from "@/data/screens/classes";

const Classes = () => {
  const [selectedTab, setSelectedTab] = useState<"classes" | "appointment">(
    "classes"
  );
  const [selectedDate, setSelectedDate] = useState(new Date());

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ClassesHeader />
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <VStack space="md" className="pb-6">
            <HorizontalDatePicker
              selectedDate={selectedDate}
              onDateSelect={setSelectedDate}
            />
            <ClassesTabs
              selectedTab={selectedTab}
              onTabSelect={setSelectedTab}
            />
            <VStack space="sm" className="px-4">
              {classesData.map((classItem: ClassItem) => (
                <ClassCard key={classItem.id} {...classItem} />
              ))}
            </VStack>
          </VStack>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
};

export default Classes;
