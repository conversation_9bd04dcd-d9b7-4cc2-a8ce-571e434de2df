import React from "react";
import { useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { ArrowLeft } from "lucide-react-native";
import ClassDetails from "@/components/screens/classes/class-details";
import { classesData } from "@/data/screens/classes";

const ClassDetailsScreen = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  
  // Find the class by ID
  const classItem = classesData.find((item) => item.id === id);

  if (!classItem) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Text className="text-lg font-dm-sans-bold text-typography-900 mb-2">
            Class Not Found
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 text-center mb-6">
            The class you're looking for doesn't exist or has been removed.
          </Text>
          <Pressable
            onPress={() => router.back()}
            className="bg-primary-500 px-6 py-3 rounded-lg"
          >
            <Text className="text-white font-dm-sans-medium">Go Back</Text>
          </Pressable>
        </VStack>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        {/* Header with back button */}
        <HStack className="items-center px-4 py-3 border-b border-background-200">
          <Pressable
            onPress={() => router.back()}
            className="p-2 -ml-2 mr-2"
          >
            <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
          </Pressable>
          <Text className="text-lg font-dm-sans-bold text-typography-900 flex-1">
            Class Details
          </Text>
        </HStack>

        {/* Class Details Component */}
        <ClassDetails classItem={classItem} />
      </VStack>
    </SafeAreaView>
  );
};

export default ClassDetailsScreen;
